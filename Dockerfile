FROM node:20 AS build_image

RUN apt-get update && apt-get install -y 

# Set the working directory
WORKDIR /usr/src/backend-recipe-ms

RUN apt-get update && apt-get install -y libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 libgbm1  libasound2 libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 libdrm2  libfontconfig1 libgtk-3-0 libnspr4 libnss3 libxshmfence1 xdg-utils

# Copy only package.json and package-lock.json to install dependencies
COPY package.json ./

# Install dependencies
RUN npm install --force

# Copy the rest of the application files
COPY . .

# Build the Node.js app
RUN yarn build

RUN npx puppeteer browsers install chrome

# remove dev dependencies
RUN npm prune --production

# Stage 2: Production Image
FROM node:20

# Set the working directory
WORKDIR /usr/src/backend-recipe-ms

RUN apt-get update && apt-get install -y libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 libgbm1  libasound2 libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 libdrm2  libfontconfig1 libgtk-3-0 libnspr4 libnss3 libxshmfence1 xdg-utils

# Install production dependencies only
COPY --from=build_image /usr/src/backend-recipe-ms/package.json ./package.json
COPY --from=build_image /usr/src/backend-recipe-ms/node_modules ./node_modules
COPY --from=build_image /usr/src/backend-recipe-ms/build ./build
COPY --from=build_image /usr/src/backend-recipe-ms/src/templates ./build/src/templates
COPY --from=build_image /root/.cache/puppeteer /root/.cache/puppeteer


# Expose the port that the Node.js app runs on
EXPOSE 9026

# Start the Node.js app
CMD ["node", "./build/src/index.js"]