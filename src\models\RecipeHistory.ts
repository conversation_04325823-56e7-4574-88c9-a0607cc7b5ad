import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeHistoryAction {
  created = "created",
  updated = "updated",
  deleted = "deleted",
  published = "published",
  archived = "archived",
  restored = "restored",
  ingredient_added = "ingredient_added",
  ingredient_removed = "ingredient_removed",
  ingredient_updated = "ingredient_updated",
  step_added = "step_added",
  step_removed = "step_removed",
  step_updated = "step_updated",
  category_added = "category_added",
  category_removed = "category_removed",
  attribute_added = "attribute_added",
  attribute_removed = "attribute_removed",
  resource_added = "resource_added",
  resource_removed = "resource_removed",
  bookmark_added = "bookmark_added",
  bookmark_removed = "bookmark_removed",
}

interface RecipeHistoryAttributes {
  id?: number;
  recipe_id: number;
  action: RecipeHistoryAction;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  organization_id?: string;
  created_by: number;
  created_at?: Date;
}

export class RecipeHistory
  extends Model<RecipeHistoryAttributes, never>
  implements RecipeHistoryAttributes {
  id!: number;
  recipe_id!: number;
  action!: RecipeHistoryAction;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  organization_id?: string;
  created_by!: number;
  created_at!: Date;

  // Define the associate method
  static associate: (models: any) => void;
}

RecipeHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    action: {
      type: DataTypes.ENUM(...Object.values(RecipeHistoryAction)),
      allowNull: false,
    },
    field_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "Name of the field that was changed",
    },
    old_value: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "Previous value before change (JSON string for complex objects)",
    },
    new_value: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "New value after change (JSON string for complex objects)",
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "Human-readable description of the change",
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      comment: "IP address of the user who made the change",
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "User agent string of the browser/client",
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: "Timestamp when the history record was created",
    }
  },
  {
    sequelize,
    tableName: "mo_recipe_history",
    modelName: "RecipeHistory",
    timestamps: false, // We handle created_at manually
    indexes: [
      {
        fields: ["recipe_id"],
        name: "idx_recipe_history_recipe",
      },
      {
        fields: ["action"],
        name: "idx_recipe_history_action",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_history_created_by",
      },
      {
        fields: ["created_at"],
        name: "idx_recipe_history_created_at",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_history_organization",
      },
      {
        fields: ["recipe_id", "created_at"],
        name: "idx_recipe_history_recipe_date",
      },
    ],
  }
);

// Define associations
RecipeHistory.associate = (models: any) => {
  // RecipeHistory belongs to Recipe
  RecipeHistory.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeHistory belongs to User (created_by)
  RecipeHistory.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });
};

export default RecipeHistory;
