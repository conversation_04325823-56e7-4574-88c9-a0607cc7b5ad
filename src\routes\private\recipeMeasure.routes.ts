import express from "express";
import uploadService from "../../helper/upload.service";
import recipeMeasureController from "../../controller/recipeMeasure.controller";
import recipeMeasureValidator from "../../validators/recipeMeasure.validator";
import { RECIPE_FILE_UPLOAD_CONSTANT } from "../../helper/common";

// 🚀 Configure S3 Upload for Recipe Measures using Constants
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  RECIPE_FILE_UPLOAD_CONSTANT.UNIT_ICON.folder
);

const router = express.Router();

/**
 * Recipe Measure Units CRUD Operations with Integrated File Upload
 * @route /api/v1/private/recipe-measures
 */

/**
 * @swagger
 * /private/recipe-measures/list:
 *   get:
 *     tags:
 *       - Recipe Measures
 *     summary: Get all recipe measure units
 *     description: Retrieve all recipe measurement units with filtering, searching, and pagination
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - name: search
 *         in: query
 *         description: Search term for unit title or description
 *         required: false
 *         schema:
 *           type: string
 *       - name: unit_status
 *         in: query
 *         description: Filter by unit status
 *         required: false
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *       - name: hasIcon
 *         in: query
 *         description: Filter by presence of icon
 *         required: false
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: sort
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [unit_title, unit_status, created_at, updated_at]
 *           default: unit_title
 *       - name: order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: Recipe measures retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe measures retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     units:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/RecipeMeasure'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/list", recipeMeasureController.getAllRecipeMeasures);

/**
 * @swagger
 * /private/recipe-measures/get/{id}:
 *   get:
 *     tags:
 *       - Recipe Measures
 *     summary: Get recipe measure by ID
 *     description: Retrieve a single recipe measurement unit by its ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe measure ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Recipe measure retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe measure retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/RecipeMeasure'
 *       404:
 *         description: Recipe measure not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/get/:id",
  recipeMeasureValidator.getRecipeMeasureValidator(),
  recipeMeasureController.getRecipeMeasureById
);

/**
 * @swagger
 * /private/recipe-measures/create:
 *   post:
 *     tags:
 *       - Recipe Measures
 *     summary: Create a new recipe measure
 *     description: Create a new recipe measurement unit with optional icon upload
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - unit_title
 *             properties:
 *               unit_title:
 *                 type: string
 *                 example: "Grams"
 *                 minLength: 2
 *                 maxLength: 100
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               unitIcon:
 *                 type: string
 *                 format: binary
 *                 description: "Unit icon image file"
 *     responses:
 *       201:
 *         description: Recipe measure created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe measure created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/RecipeMeasure'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create",
  multerS3Upload.upload("unitIcon"),
  recipeMeasureValidator.createRecipeMeasureValidator(),
  recipeMeasureController.createRecipeMeasure
);

/**
 * @swagger
 * /private/recipe-measures/update/{id}:
 *   put:
 *     tags:
 *       - Recipe Measures
 *     summary: Update recipe measure
 *     description: Update an existing recipe measurement unit with optional icon upload
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe measure ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               unit_title:
 *                 type: string
 *                 example: "Updated Grams"
 *                 minLength: 2
 *                 maxLength: 100
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               unitIcon:
 *                 type: string
 *                 format: binary
 *                 description: "Unit icon image file"
 *     responses:
 *       200:
 *         description: Recipe measure updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe measure updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/RecipeMeasure'
 *       404:
 *         description: Recipe measure not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/update/:id",
  multerS3Upload.upload("unitIcon"),
  recipeMeasureValidator.updateRecipeMeasureValidator(),
  recipeMeasureController.updateRecipeMeasure
);

/**
 * @swagger
 * /private/recipe-measures/delete/{id}:
 *   delete:
 *     tags:
 *       - Recipe Measures
 *     summary: Delete recipe measure
 *     description: Soft delete a recipe measurement unit by ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe measure ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Recipe measure deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe measure deleted successfully"
 *       404:
 *         description: Recipe measure not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete(
  "/delete/:id",
  recipeMeasureValidator.deleteRecipeMeasureValidator(),
  recipeMeasureController.deleteRecipeMeasure
);

export default router;
