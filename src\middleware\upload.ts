import multer from 'multer';

// Configure multer for memory storage
const storage = multer.memoryStorage();

// Setup file filter for allowed mime types
const fileFilter = (req: any, file: Express.Multer.File, cb: any) => {
  // Accept images and documents
  if (
    file.mimetype.startsWith('image/') ||
    file.mimetype === 'application/pdf' ||
    file.mimetype === 'application/msword' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
    file.mimetype === 'text/plain'
  ) {
    cb(null, true);
  } else {
    cb(new Error('Unsupported file type'), false);
  }
};

// Enhanced file filter for batch file uploads (supports more file types)
const enhancedFileFilter = (req: any, file: Express.Multer.File, cb: any) => {
  // Accept images, documents, videos, and audio files
  if (
    file.mimetype.startsWith('image/') ||
    file.mimetype.startsWith('video/') ||
    file.mimetype.startsWith('audio/') ||
    file.mimetype === 'application/pdf' ||
    file.mimetype === 'application/msword' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
    file.mimetype === 'application/vnd.ms-excel' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.mimetype === 'application/vnd.ms-powerpoint' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
    file.mimetype === 'text/plain' ||
    file.mimetype === 'text/csv'
  ) {
    cb(null, true);
  } else {
    cb(new Error('Unsupported file type. Allowed types: images, videos, audio, PDF, Word, Excel, PowerPoint, and text files'), false);
  }
};

// Create multer upload instance with options (original - for backward compatibility)
const upload = multer({
  storage,
  fileFilter,
});

// Enhanced multer upload instance for batch file operations
const enhancedUpload = multer({
  storage,
  fileFilter: enhancedFileFilter
});

export default upload;
export { enhancedUpload };
