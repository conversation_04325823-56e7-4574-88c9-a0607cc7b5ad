import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { sequelize, db } from "../models";
import { MeasureStatus } from "../models/RecipeMeasure";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { IngredientConversionStatus } from "../models/IngredientConversion";
import { IngredientStatus } from "../models/Ingreditant";
import { generateUniqueSlug } from "../helper/slugGenerator";
import {
  isDefaultAccess,
  getPaginatedItems,
  getUserFullName,
} from "../helper/common";

// Get models from db object to ensure associations are set up
const RecipeMeasure = db.RecipeMeasure;
const Item = db.Item;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const IngredientConversion = db.IngredientConversion;
const Ingredient = db.Ingredient;

// Helper function to get the proper base URL
const getBaseUrl = (): string => {
  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    // API_BASE_URL already contains the full endpoint, return base part
    return baseUrl.replace(
      "/backend-api/v1/public/user/get-file?location=",
      ""
    );
  } else {
    // For development or when API_BASE_URL is just the base domain
    return (
      process.env.BASE_URL ||
      process.env.FRONTEND_URL ||
      "https://staging.namastevillage.theeasyaccess.com"
    );
  }
};

/**
 * Create a new recipe measure unit
 * @route POST /api/v1/private/recipe-measure
 * @access Private (Authenticated users)
 */
const createRecipeMeasure = async (req: any, res: Response): Promise<any> => {
  try {
    const { unit_title, status } = req.body;

    // Check if user has default access (can create system defaults)
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Determine organization ID for creation
    const organizationIdForCreation = hasDefaultAccess
      ? null
      : req.user?.organization_id;

    // Check for duplicate unit title (case-insensitive)
    // Check both in same organization AND in default/system records
    const existingUnitByTitle = await RecipeMeasure.findOne({
      where: {
        [Op.and]: [
          db.sequelize.where(
            db.sequelize.fn("LOWER", db.sequelize.col("unit_title")),
            db.sequelize.fn("LOWER", unit_title.trim())
          ),
          {
            [Op.or]: [
              { organization_id: organizationIdForCreation }, // Same organization
              { organization_id: null }, // Default/system records
            ],
          },
        ],
      },
    });

    if (existingUnitByTitle) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("UNIT_TITLE_ALREADY_EXISTS"),
      });
    }

    // Generate unique slug from unit title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existing = await RecipeMeasure.findOne({
        where: {
          unit_slug: slug,
          organization_id: organizationIdForCreation,
        },
      });
      return !!existing;
    };

    const unitSlug = await generateUniqueSlug(unit_title, checkSlugExists, {
      maxLength: 25,
      separator: "-",
      lowercase: true,
    });

    // Set system unit flag based on default access
    const isSystemUnit = hasDefaultAccess;

    // Create unit first
    const unit = await RecipeMeasure.create({
      unit_title: unit_title,
      unit_slug: unitSlug,
      status: status || MeasureStatus.active,
      organization_id: organizationIdForCreation,
      is_system_unit: isSystemUnit,
      created_by: req.user?.id || null,
      updated_by: req.user?.id || null,
    });

    let iconItemId = null;

    // Handle file upload from S3 middleware (req.files format)
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      if (files.unitIcon && files.unitIcon.length > 0) {
        const uploadedFile = files.unitIcon[0];
        iconItemId = uploadedFile.item_id;
      }
    }

    // Update unit with icon item_id if uploaded
    if (iconItemId) {
      await unit.update({ unit_icon: iconItemId });
    }

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("UNIT_CREATED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in recipeMeasure.controller.ts - createRecipeMeasure:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Get all recipe measure units with filtering and search
 * @route GET /api/v1/private/recipe-measures/list
 * @access Private (Authenticated users)
 *
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: show all if not provided)
 * - search: Search in unit title and slug
 * - status: Filter by unit status (active, inactive)
 * - isSystemUnit: Filter by system units (true/false) - Admin only
 * - sort: Sort field (default: unit_title)
 * - order: Sort order (ASC/DESC, default: ASC)
 */
const getAllRecipeMeasures = async (req: any, res: Response): Promise<any> => {
  try {
    const {
      page,
      limit,
      search = "",
      status,
      isSystemUnit,
      sort = "unit_title",
      order = "ASC",
    } = req.query;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause with organization isolation
    let whereClause: any = {};

    if (hasDefaultAccess) {
      // Users with default access see all records (no filter)
      whereClause = {};
    } else {
      // Regular users see their org records + system defaults
      whereClause = {
        [Op.or]: [
          { organization_id: req.user?.organization_id },
          { organization_id: null }, // System defaults
          { is_system_unit: true }, // System units
        ],
      };
    }

    // Add search functionality
    if (search) {
      whereClause[Op.or] = [
        { unit_title: { [Op.like]: `%${search}%` } },
        { unit_slug: { [Op.like]: `%${search}%` } },
      ];
    }

    // Filter by status
    if (status) {
      whereClause.status = status;
    }

    // Filter by system unit flag - accessible by all users
    if (isSystemUnit !== undefined) {
      if (isSystemUnit === "true") {
        whereClause.is_system_unit = true;
      } else if (isSystemUnit === "false") {
        whereClause.is_system_unit = false;
      }
      // If isSystemUnit is neither "true" nor "false", ignore the filter
    }

    // Handle pagination - if limit is not provided, show all records
    const pageNumber = page ? Number(page) : 1;
    const limitNumber = limit ? Number(limit) : null; // null means no limit
    const offset = limitNumber ? (pageNumber - 1) * limitNumber : 0;

    // Build query options - conditionally add limit and offset
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: Item,
          as: "iconItem",
          attributes: [
            "id",
            "item_name",
            "item_location",
            "item_mime_type",
            // Add computed iconUrl field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
              "iconUrl",
            ],
            // Add computed hasIcon field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
              "hasIcon",
            ],
          ],
          required: false, // LEFT JOIN to include units without icons
        },
      ],
      order: [[sort as string, order as string]],
      raw: false,
      nest: true,
    };

    // Only add limit and offset if limit is provided
    if (limitNumber) {
      queryOptions.limit = limitNumber;
      queryOptions.offset = offset;
    }

    // Fetch units with conditional pagination
    const { rows: units, count } =
      await RecipeMeasure.findAndCountAll(queryOptions);

    // Add user names to the units
    const unitsWithUserNames = await Promise.all(
      units.map(async (unit: any) => {
        const unitData = unit.toJSON ? unit.toJSON() : unit;

        // Add created_by and updated_by user names
        if (unitData.created_by) {
          unitData.created_by_name = await getUserFullName(unitData.created_by);
        }
        if (unitData.updated_by) {
          unitData.updated_by_name = await getUserFullName(unitData.updated_by);
        }

        return unitData;
      })
    );

    // Calculate pagination info only if limit is provided
    let paginationInfo: any = {
      count: count,
      data: unitsWithUserNames,
    };

    if (limitNumber) {
      const { total_pages } = getPaginatedItems(
        limitNumber,
        pageNumber,
        count || 0
      );

      paginationInfo = {
        ...paginationInfo,
        page: pageNumber,
        limit: limitNumber,
        total_pages: total_pages,
      };
    } else {
      // When no limit is provided, show all records info
      paginationInfo = {
        ...paginationInfo,
        page: 1,
        limit: "all",
        total_pages: 1,
        showing_all_records: true,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      ...paginationInfo,
    });
  } catch (error) {
    console.log(
      "Error in recipeMeasure.controller.ts - getAllRecipeMeasures:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Get single recipe measure unit by ID
 * @route GET /api/v1/private/recipe-measure/:id
 * @access Private (Authenticated users)
 */
const getRecipeMeasureById = async (req: any, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only see their org records + system defaults
      whereClause = {
        id,
        [Op.or]: [
          { organization_id: req.user?.organization_id },
          { organization_id: null }, // System defaults
          { is_system_unit: true }, // System units
        ],
      };
    }

    const unit = await RecipeMeasure.findOne({
      where: whereClause,
      include: [
        {
          model: Item,
          as: "iconItem",
          attributes: [
            "id",
            "item_name",
            "item_location",
            "item_mime_type",
            // Add computed iconUrl field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
              "iconUrl",
            ],
            // Add computed hasIcon field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
              "hasIcon",
            ],
          ],
          required: false, // LEFT JOIN to include unit even without icon
        },
      ],
      raw: false,
      nest: true,
    });

    if (!unit) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("UNIT_NOT_FOUND"),
        data: {},
      });
    }

    // Add user names to the unit
    const unitData = unit.toJSON ? unit.toJSON() : unit;

    // Add created_by and updated_by user names
    if (unitData.created_by) {
      unitData.created_by_name = await getUserFullName(unitData.created_by);
    }
    if (unitData.updated_by) {
      unitData.updated_by_name = await getUserFullName(unitData.updated_by);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: unitData,
    });
  } catch (error) {
    console.log(
      "Error in recipeMeasure.controller.ts - getRecipeMeasureById:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Update recipe measure unit by ID
 * @route PUT /api/v1/private/recipe-measure/:id
 * @access Private (Authenticated users)
 */
const updateRecipeMeasure = async (req: any, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { unit_title, status } = req.body;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only update their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can update any record
      whereClause = { id };
    }

    // Find the unit
    const unit = await RecipeMeasure.findOne({
      where: whereClause,
    });

    if (!unit) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("UNIT_NOT_FOUND"),
      });
    }

    // Prevent regular users from updating system default records
    if (unit.organization_id === null && !hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
      });
    }

    // 🔍 BUSINESS VALIDATION: Check for duplicate unit title (excluding current unit)
    if (unit_title && unit_title.trim() !== unit.unit_title) {
      // For system defaults, check globally; for org records, check within org
      const duplicateCheckOrgId =
        hasDefaultAccess && unit.organization_id === null
          ? null
          : req.user?.organization_id;

      // Check both in same organization AND in default/system records
      const existingUnitByTitle = await RecipeMeasure.findOne({
        where: {
          [Op.and]: [
            db.sequelize.where(
              db.sequelize.fn("LOWER", db.sequelize.col("unit_title")),
              db.sequelize.fn("LOWER", unit_title.trim())
            ),
            {
              [Op.or]: [
                { organization_id: req.user?.organization_id }, // Same organization
                { organization_id: null }, // Default/system records
              ],
            },
            { id: { [Op.ne]: id } }, // Exclude current unit
          ],
        },
      });

      if (existingUnitByTitle) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("UNIT_TITLE_ALREADY_EXISTS"),
        });
      }
    }

    // Generate new slug if unit title is being updated
    let newSlug = unit.unit_slug;
    if (unit_title && unit_title !== unit.unit_title) {
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        // For system defaults, check globally; for org records, check within org
        const slugCheckOrgId =
          hasDefaultAccess && unit.organization_id === null
            ? null
            : req.user?.organization_id;

        const existing = await RecipeMeasure.findOne({
          where: {
            unit_slug: slug,
            organization_id: slugCheckOrgId,
            id: { [Op.ne]: id },
          },
        });
        return !!existing;
      };

      newSlug = await generateUniqueSlug(unit_title, checkSlugExists, {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      });
    }

    let newIconItemId = unit.unit_icon;
    let iconWasUpdated = false;

    // Handle file upload from S3 middleware (req.files format)
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      if (files.unitIcon && files.unitIcon.length > 0) {
        const uploadedFile = files.unitIcon[0];
        newIconItemId = uploadedFile.item_id;
        iconWasUpdated = true;
      } else if (files.unitIcon && files.unitIcon.length === 0) {
        // Empty file array means user wants to remove the icon
        newIconItemId = null;
        iconWasUpdated = true;
      }
    } else if (req.body.unitIcon === "" || req.body.unitIcon === null) {
      // Handle explicit removal of icon via form data
      newIconItemId = null;
      iconWasUpdated = true;
    }

    // Update the unit
    await RecipeMeasure.update(
      {
        unit_title: unit_title || unit.unit_title,
        unit_slug: newSlug,
        unit_icon: newIconItemId,
        status: status || unit.status,
        updated_by: req.user?.id || null,
      },
      {
        where: whereClause,
      }
    );

    // Fetch updated unit using the same where clause
    const updatedUnit = await RecipeMeasure.findOne({
      where: whereClause,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("UNIT_UPDATED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in recipeMeasure.controller.ts - updateRecipeMeasure:",
      error
    );
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Delete recipe measure unit by ID (hard delete if not in use, otherwise show usage message)
 * @route DELETE /api/v1/private/recipe-measure/:id
 * @access Private (Authenticated users)
 */
const deleteRecipeMeasure = async (req: any, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only delete their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can delete any record
      whereClause = { id };
    }

    const unit = await RecipeMeasure.findOne({
      where: whereClause,
      transaction,
    });

    if (!unit) {
      await transaction.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("UNIT_NOT_FOUND"),
      });
    }

    // Prevent deletion of system default records (organization_id: null) by non-default users
    if (unit.organization_id === null && !hasDefaultAccess) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_DELETE_SYSTEM_DEFAULT"),
      });
    }

    // Prevent deletion of system units by non-default users
    if (unit.is_system_unit && !hasDefaultAccess) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_DELETE_SYSTEM_UNIT"),
      });
    }

    // Check if measure is being used in recipe attributes (any status)
    const recipeAttributeUsage = await RecipeAttributes.count({
      where: {
        unit_of_measure: id,
      },
      transaction,
    });

    // Check if measure is being used in recipe ingredients (any status)
    const recipeIngredientUsage = await RecipeIngredients.count({
      where: {
        ingredient_measure: id,
      },
      transaction,
    });

    // Check if measure is being used in ingredient conversions (any status)
    const ingredientConversionUsage = await IngredientConversion.count({
      where: {
        [Op.or]: [{ from_measure: id }, { to_measure: id }],
      },
      transaction,
    });

    // Check if measure is being used as unit_of_measure in ingredients (any status)
    const ingredientUsage = await Ingredient.count({
      where: {
        unit_of_measure: id,
      },
      transaction,
    });

    // Calculate total usage
    const totalUsage =
      recipeAttributeUsage +
      recipeIngredientUsage +
      ingredientConversionUsage +
      ingredientUsage;

    // If measure is in use, prevent deletion
    if (totalUsage > 0) {
      await transaction.rollback();

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("UNIT_IN_USE_CANNOT_DELETE"),
      });
    }

    // Hard delete the measure and all its inactive relations
    await RecipeAttributes.destroy({
      where: { unit_of_measure: id },
      transaction,
    });

    await RecipeIngredients.update(
      { ingredient_measure: null },
      {
        where: { ingredient_measure: id },
        transaction,
      }
    );

    await IngredientConversion.destroy({
      where: {
        [Op.or]: [{ from_measure: id }, { to_measure: id }],
      },
      transaction,
    });

    await Ingredient.update(
      { unit_of_measure: null },
      {
        where: { unit_of_measure: id },
        transaction,
      }
    );

    await RecipeMeasure.destroy({
      where: whereClause,
      transaction,
    });

    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("UNIT_DELETED_SUCCESSFULLY"),
    });
  } catch (error) {
    console.log(
      "Error in recipeMeasure.controller.ts - deleteRecipeMeasure:",
      error
    );
    await transaction.rollback();
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

// Default export object
export default {
  createRecipeMeasure,
  getAllRecipeMeasures,
  getRecipeMeasureById,
  updateRecipeMeasure,
  deleteRecipeMeasure,
};
