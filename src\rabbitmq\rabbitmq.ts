import amqp from "amqplib";
// import { RABBITMQ_QUEUE } from "../helper/constant";
// import { createNotification } from "../helper/common";

let connection: any = null;
const RABBITMQ_URL = process.env.RABBITMQ_URL;
/** The getConnection() function in this code creates and reuses a single connection for RabbitMQ: */
const getConnection = async () => {
  if (!connection) {
    connection = await amqp.connect(RABBITMQ_URL);
  }
  return connection;
};

/* Create a new channel for each queue or operation 
 A single connection can have multiple channels.
*/
const createChannel = async () => {
  const conn = await getConnection();
  return await conn.createChannel();
};

const consumeMessage = async (queue: any) => {
  try {
    const channel = await createChannel();

    // Assert the queue (ensure the queue exists)
    await channel.assertQueue(queue, { durable: true });
    console.log(`Waiting for messages in queue: ${queue}`);

    // Consume the message
    channel.consume(queue, async (msg: any) => {
      if (msg !== null) {
        // const message = JSON.parse(msg.content.toString());
        const routingKey = msg.fields.routingKey;
        console.log(`Received message with routingKey: ${routingKey}`);
        try {
          // Handle the message based on the routing key


          channel.ack(msg);
        } catch (error) {
          console.error(
            `Error handling message for routingKey ${routingKey}:`,
            error,
          );
          // Optionally, reject the message instead of acknowledging it
          channel.nack(msg, false, false); // Requeue: false
        }
      }
    });
  } catch (error) {
    console.error(`Error handling message`, error);
  }
};

const publishMessage = async (queue: string, message: any) => {
  const channel = await createChannel();
  try {
    await channel.assertQueue(queue);
    channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)), {
      persistent: true,
    });
    console.log(`Message published to queue "${queue}"`);
  } catch (err) {
    console.error("Error publishing message:", err);
  } finally {
    await channel.close();
  }
};

export default { consumeMessage, publishMessage, createChannel };
