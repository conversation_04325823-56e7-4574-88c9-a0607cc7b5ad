import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum IngredientCategoryStatus {
  active = "active",
  inactive = "inactive",
}

export class IngredientCategory extends Model {
  category_id!: number;
  ingredient_id!: number;
  ingredient_category_status!: IngredientCategoryStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
}

IngredientCategory.init(
  {
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_category",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    ingredient_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_ingredients",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    ingredient_category_status: {
      type: DataTypes.ENUM,
      values: Object.values(IngredientCategoryStatus),
      defaultValue: IngredientCategoryStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_ingredients_category",
    modelName: "IngredientCategory",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["category_id", "ingredient_id"],
        name: "primary_ingredient_category",
      },
      {
        fields: ["organization_id"],
        name: "idx_ingredient_category_organization",
      },
      {
        fields: ["ingredient_category_status"],
        name: "idx_ingredient_category_status",
      },
    ],
  }
);

// Define associations
IngredientCategory.associate = (models: any) => {
  // IngredientCategory belongs to Ingredient
  IngredientCategory.belongsTo(models.Ingredient, {
    foreignKey: "ingredient_id",
    as: "ingredient",
  });

  // IngredientCategory belongs to Category
  IngredientCategory.belongsTo(models.Category, {
    foreignKey: "category_id",
    as: "category",
  });
};
