import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum CategoryStatus {
  active = "active",
  inactive = "inactive",
}

export enum CategoryType {
  recipe = "recipe",
  ingredient = "ingredient",
}

interface CategoryAttributes {
  id?: number;
  category_name: string;
  category_slug: string;
  category_description?: string;
  category_icon?: number;
  category_status: CategoryStatus;
  organization_id?: string;
  category_type: CategoryType;
  is_system_category: boolean;
  created_by: number;
  updated_by: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Category
  extends Model<CategoryAttributes, never>
  implements CategoryAttributes {
  id!: number;
  category_name!: string;
  category_slug!: string;
  category_description?: string;
  category_icon?: number;
  category_status!: CategoryStatus;
  organization_id?: string;
  category_type!: CategoryType;
  is_system_category!: boolean;
  created_by!: number;
  updated_by!: number;
  createdAt!: Date;
  updatedAt!: Date;
}

Category.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    category_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    category_slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    category_description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category_icon: {
      type: DataTypes.INTEGER, // Store item_id reference
      allowNull: true,
      comment: "Foreign key reference to nv_items table for category icon",
      references: {
        model: "nv_items",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    category_status: {
      type: DataTypes.ENUM(Object.values(CategoryStatus)),
      allowNull: false,
      defaultValue: CategoryStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    category_type: {
      type: DataTypes.ENUM(Object.values(CategoryType)),
      allowNull: false,
    },
    is_system_category: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_category",
    modelName: "Category",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

// Define associations
Category.associate = (models: any) => {
  // Category belongs to User (created_by)
  Category.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // Category belongs to User (updated_by)
  Category.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  // Many-to-many association with Ingredient through IngredientCategory
  Category.belongsToMany(models.Ingredient, {
    through: models.IngredientCategory,
    foreignKey: "category_id",
    otherKey: "ingredient_id",
    as: "ingredients",
  });

  // Many-to-many association with Recipe through RecipeCategory
  Category.belongsToMany(models.Recipe, {
    through: models.RecipeCategory,
    foreignKey: "category_id",
    otherKey: "recipe_id",
    as: "recipes",
  });

  // Category belongs to Item (category_icon) - Proper foreign key constraint
  Category.belongsTo(models.Item, {
    foreignKey: "category_icon",
    as: "iconItem",
    constraints: true,
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  });
};

export default Category;
