module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  plugins: ['@typescript-eslint'],
  extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended'],
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  rules: {
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/consistent-type-definitions': 'warn',
    '@typescript-eslint/no-explicit-any': 'off'
  },
  overrides: [
    {
      // Special rules for seeder and migration files
      files: ['src/seeders/**/*.js', 'src/migrations/**/*.js'],
      env: {
        node: true,
        commonjs: true,
      },
      rules: {
        // Allow CommonJS in seeders and migrations
        'no-undef': 'off',
        '@typescript-eslint/no-require-imports': 'off',
        '@typescript-eslint/no-var-requires': 'off',
      },
      parserOptions: {
        sourceType: 'script', // Use script mode for CommonJS
      },
    },
  ],
};
