import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeCategoryStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeCategoryAttributes {
  recipe_id: number;
  category_id: number;
  status: RecipeCategoryStatus;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeCategory
  extends Model<RecipeCategoryAttributes, never>
  implements RecipeCategoryAttributes
{
  recipe_id!: number;
  category_id!: number;
  status!: RecipeCategoryStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeCategory.init(
  {
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_category",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeCategoryStatus)),
      allowNull: false,
      defaultValue: RecipeCategoryStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_category",
    modelName: "RecipeCategory",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    // Composite primary key
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "category_id"],
        name: "primary_recipe_category",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_category_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_category_status",
      },
    ],
  }
);

// Define associations
RecipeCategory.associate = (models: any) => {
  // RecipeCategory belongs to Recipe
  RecipeCategory.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeCategory belongs to Category
  RecipeCategory.belongsTo(models.Category, {
    foreignKey: "category_id",
    as: "category",
  });

  // RecipeCategory belongs to User (created_by)
  RecipeCategory.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeCategory belongs to User (updated_by)
  RecipeCategory.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });
};

export default RecipeCategory;
