import { getUserRoles } from "../keycloak/common";
import fs from "fs";
import { User } from "../models/User";
import { QueryTypes, sequelize } from "../models";
import { createHash } from "crypto";
import * as path from "path";
import { ROLE_CONSTANT } from "./constant";

/** read html file */
const readHTMLFile = function (path: any, cb: any) {
  /** Read the HTML file at the specified path using UTF-8 encoding */
  fs.readFile(path, "utf-8", function (err, data) {
    /** If an error occurs during reading, log the error and throw it */
    if (err) {
      console.log(err);
      throw err; // Stop the execution and throw the error
    } else {
      /** If no error, pass the file content to the callback function */
      cb(null, data); // call the callback function with the file data
    }
  });
};

/** Get UserId from User table */
const getUserIdFromUserTable = async (userId: any) => {
  try {
    const getUser: any = await User.findOne({
      where: {
        keycloak_userId: userId,
      },
      raw: true,
    });
    if (!getUser) {
      return { status: false, message: "ERROR_USER_NOT_FOUND_IN_KEYCLOAK" };
    }
    return { status: true, data: getUser };
  } catch (e) {
    console.log("Exception: ", e);
    return null;
  }
};

const getPagination = (page: number, size: number) => {
  const limit = size;
  const Page = page || 1;
  const offset = (Page - 1) * limit;
  return { limit, offset };
};

const getPaginatedItems = (
  pageSize: number,
  pageNumber: number,
  total: number
) => {
  return {
    pageNumber: pageNumber,
    per_page: pageSize,
    total: total,
    total_pages: Math.ceil(total / pageSize),
  };
};

/** Check user role status and return response accordingly. */
const checkUserRole = async (userId: any, token: any) => {
  try {
    const getRoles: any = await getUserRoles(userId, token);
    const masterRole: any = getRoles.data.realmMappings.find(
      (role: any) =>
        role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
        role.description === global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION
    );

    if (!masterRole) {
      return false;
    }
    return true;
  } catch (e) {
    console.log("user role status Exception: ", e);
    return { status: false, message: e };
  }
};

const getUser = async (id: any, isAuth: boolean = false) => {
  const findUser = await sequelize.query(
    `SELECT id, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id , keycloak_auth_id FROM nv_users WHERE ${isAuth ? `keycloak_auth_id='${id}'` : `id = ${id}`} AND user_status NOT IN ('cancelled', 'deleted')`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );

  return findUser && findUser.length > 0 ? findUser[0] : null;
};

/**
 * Get user full name by user ID
 * @param user_id - User ID
 * @returns Promise<string> - User full name or fallback
 */
const getUserFullName = async (user_id: any): Promise<string> => {
  try {
    if (!user_id) return "Unknown User";

    const findUser: any = await sequelize.query(
      `SELECT user_first_name, user_middle_name, user_last_name, user_email
       FROM nv_users
       WHERE id = ${user_id}
       AND user_status NOT IN ('cancelled', 'deleted')`,
      {
        type: QueryTypes.SELECT,
        raw: true,
      }
    );

    if (!findUser || findUser.length === 0) return "Unknown User";

    const user = findUser[0];
    const employeeName = [];

    if (user?.user_first_name) {
      employeeName.push(user.user_first_name);
    }
    if (user?.user_middle_name) {
      employeeName.push(user.user_middle_name);
    }
    if (user?.user_last_name) {
      employeeName.push(user.user_last_name);
    }

    // If no name parts found, use email as fallback
    if (employeeName.length === 0 && user?.user_email) {
      return user.user_email.split("@")[0]; // Use part before @ as display name
    }

    return employeeName.length > 0 ? employeeName.join(" ") : "Unknown User";
  } catch (error) {
    console.log("Error getting user full name:", error);
    return "Unknown User";
  }
};

const getUsers = async (ids: any) => {
  const findUsers = await sequelize.query(
    `SELECT id, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id FROM nv_users WHERE id IN (${ids.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return findUsers;
};

const getRoles = async (id: any) => {
  const findRoles = await sequelize.query(
    `SELECT * FROM nv_roles WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return findRoles;
};

const getBranchDetails = async (id: any) => {
  const findBranches = await sequelize.query(
    `SELECT * FROM nv_branches WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return findBranches;
};

const getDepartmentDetails = async (id: any) => {
  const findBranches = await sequelize.query(
    `SELECT * FROM nv_departments WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return findBranches;
};

const getUserAllRoles = async (userId: number) => {
  const userRoles = await sequelize.query(
    `SELECT r.id, r.role_name FROM nv_user_roles ur INNER JOIN nv_roles r ON ur.role_id = r.id WHERE ur.user_id = ${userId}`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return userRoles;
};

const getUserSession = async (token: string, deviceType: string) => {
  // const userSession = await UserSession.findOne({ where: { token, device_type: deviceType } });
  const userSession = await sequelize.query(
    `SELECT * FROM nv_user_session WHERE token = '${token}' AND device_type = '${deviceType}'`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
  return userSession && userSession.length > 0 ? userSession[0] : null;
};

/**
 * Common Sequelize literal for fetching user information (ID and full name)
 * @param userIdField - The field name containing the user ID (e.g., 'created_by', 'updated_by')
 * @param aliasPrefix - Prefix for the alias (e.g., 'creator', 'updater')
 * @returns Object with user_id and user_full_name literals
 */
const getUserLiterals = (userIdField: string, aliasPrefix: string) => {
  return {
    [`${aliasPrefix}_user_id`]: sequelize.literal(`${userIdField}`),
    [`${aliasPrefix}_user_full_name`]: sequelize.literal(
      `(SELECT CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, ''))
        FROM nv_users
        WHERE id = ${userIdField}
        LIMIT 1)`
    ),
  };
};

/**
 * Enhanced user literals with additional user information
 * @param userIdField - The field name containing the user ID
 * @param aliasPrefix - Prefix for the alias
 * @returns Object with comprehensive user information literals
 */
const getEnhancedUserLiterals = (userIdField: string, aliasPrefix: string) => {
  return {
    [`${aliasPrefix}_user_id`]: sequelize.literal(`${userIdField}`),
    [`${aliasPrefix}_user_full_name`]: sequelize.literal(
      `(SELECT CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, ''))
        FROM nv_users
        WHERE id = ${userIdField}
        LIMIT 1)`
    ),
    [`${aliasPrefix}_user_email`]: sequelize.literal(
      `(SELECT user_email FROM nv_users WHERE id = ${userIdField} LIMIT 1)`
    ),
    [`${aliasPrefix}_user_avatar_link`]: sequelize.literal(
      `(SELECT
         CASE
           WHEN user_avatar IS NOT NULL AND user_avatar != ''
           THEN CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
           ELSE ''
         END
       FROM nv_users
       WHERE id = ${userIdField}
       LIMIT 1)`
    ),
  };
};

const createNotification = async (data: any) => {
  await sequelize.query(
    `INSERT INTO nv_notifications (${Object.keys(data).join(",")}) VALUES (${Object.values(
      data
    )
      .map((value) => `'${value}'`)
      .join(",")}) `,
    {
      type: QueryTypes.INSERT,
      returning: true,
    }
  );
};

const getUserWeekDays = async (userId: number, day: any) => {
  return await sequelize.query(
    `(SELECT * FROM nv_user_week_day WHERE user_id = ${userId} AND ${day} !='working' AND user_weekday_status='active')`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    }
  );
};

const getHash = function (file: any, mimeType?: string) {
  try {
    // Create a simple hash using file content, size, and mime type
    const fileContent = fs.readFileSync(file.path);
    const hash = createHash("sha256");

    // Add file content, size, and mime type to hash
    hash.update(fileContent);
    hash.update(file.size.toString());

    if (mimeType) {
      hash.update(mimeType);
    } else {
      // Simple mime type detection based on file extension
      const ext = path
        .extname(file.originalname || file.filename || "")
        .toLowerCase();
      const detectedMimeType = getMimeTypeFromExtension(ext);
      hash.update(detectedMimeType);
      mimeType = detectedMimeType;
    }

    const finalHash = hash.digest("hex");

    return {
      status: true,
      hash: finalHash,
      actualMimeType: mimeType,
    };
  } catch (error) {
    return {
      status: false,
      message: error,
    };
  }
};

const getMimeTypeFromExtension = (ext: string): string => {
  const mimeTypes: { [key: string]: string } = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".webp": "image/webp",
    ".svg": "image/svg+xml",
    ".mp4": "video/mp4",
    ".avi": "video/avi",
    ".mov": "video/quicktime",
    ".wmv": "video/x-ms-wmv",
    ".mp3": "audio/mpeg",
    ".wav": "audio/wav",
    ".pdf": "application/pdf",
    ".doc": "application/msword",
    ".docx":
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".txt": "text/plain",
    ".json": "application/json",
    ".xml": "application/xml",
  };

  return mimeTypes[ext] || "application/octet-stream";
};

const ReadingFile = (path: string) => {
  return new Promise(function (resolve, reject) {
    let FileObject: any;
    const readStream = fs.createReadStream(path, "utf-8");
    readStream.on("error", (error: any) => {
      console.log(error);
      FileObject = { status: false, data: error };
      reject(FileObject);
    });
    const chunk: any = [];
    readStream.on("data", (data: any) => chunk.push(data));
    readStream.on("end", () => {
      console.log("Reading complete");
      FileObject = { status: true, data: chunk };
      resolve(FileObject);
    });
  });
};

// Recipe File Upload Constants
export const RECIPE_FILE_UPLOAD_CONSTANT = Object.freeze({
  RECIPE_IMAGE: {
    folder: "recipe_images",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_images/${recipeId ? `${recipeId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_images/${recipeId ? `${recipeId}/` : ""}${fileName}`,
  },
  RECIPE_VIDEO: {
    folder: "recipe_videos",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_videos/${recipeId ? `${recipeId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_videos/${recipeId ? `${recipeId}/` : ""}${fileName}`,
  },
  RECIPE_DOCUMENT: {
    folder: "recipe_documents",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_documents/${recipeId ? `${recipeId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_documents/${recipeId ? `${recipeId}/` : ""}${fileName}`,
  },
  RECIPE_AUDIO: {
    folder: "recipe_audio",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_audio/${recipeId ? `${recipeId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_audio/${recipeId ? `${recipeId}/` : ""}${fileName}`,
  },
  INGREDIENT_IMAGE: {
    folder: "recipe_ingredient_images",
    destinationPath: (
      orgName: string | null,
      ingredientId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_ingredients/${ingredientId ? `${ingredientId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_ingredients/${ingredientId ? `${ingredientId}/` : ""}${fileName}`,
  },
  CATEGORY_ICON: {
    folder: "recipe_category_icons",
    destinationPath: (
      orgName: string | null,
      categoryId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_categories/${fileName}`
        : `recipe_defaults/recipe_categories/${fileName}`,
  },
  INGREDIENT_CATEGORY_ICON: {
    folder: "ingredient_category_icons",
    destinationPath: (
      orgName: string | null,
      categoryId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/ingredient_categories/${fileName}`
        : `recipe_defaults/ingredient_categories/${fileName}`,
  },
  ATTRIBUTE_ICON: {
    folder: "recipe_attribute_icons",
    destinationPath: (
      orgName: string | null,
      attributeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_food_attributes/${fileName}`
        : `recipe_defaults/recipe_food_attributes/${fileName}`,
  },
  UNIT_ICON: {
    folder: "recipe_unit_icons",
    destinationPath: (
      orgName: string | null,
      _unitId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_measures/${fileName}`
        : `recipe_defaults/recipe_measures/${fileName}`,
  },
  RECIPE_THUMBNAIL: {
    folder: "recipe_thumbnails",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_thumbnails/${recipeId ? `${recipeId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_thumbnails/${recipeId ? `${recipeId}/` : ""}${fileName}`,
  },
  RECIPE_INSTRUCTION_MEDIA: {
    folder: "recipe_instruction_media",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      stepNumber: number,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_instruction_media/${recipeId ? `${recipeId}/` : ""}step_${stepNumber}/${fileName}`
        : `recipe_defaults/recipe_instruction_media/${recipeId ? `${recipeId}/` : ""}step_${stepNumber}/${fileName}`,
  },
  RECIPE_NUTRITION_LABELS: {
    folder: "recipe_nutrition_labels",
    destinationPath: (
      orgName: string | null,
      recipeId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/recipe_nutrition_labels/${recipeId ? `${recipeId}/` : ""}${fileName}`
        : `recipe_defaults/recipe_nutrition_labels/${recipeId ? `${recipeId}/` : ""}${fileName}`,
  },
  SAMPLE_FILES: {
    folder: "sample_files",
    destinationPath: (
      orgName: string | null,
      fileType: string,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/sample_files/${fileType}/${fileName}`
        : `recipe_defaults/sample_files/${fileType}/${fileName}`,
  },
});

/**
 * Generate image URL from item_location
 * @param itemLocation - The S3 path stored in database
 * @returns Full URL to access the image via file serving endpoint
 */
const getImageUrl = (itemLocation: string | null): string | null => {
  if (!itemLocation) return null;

  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    // API_BASE_URL already contains the full endpoint, just append the file path
    // Don't include bucket name as the endpoint handles it automatically
    return `${baseUrl}${itemLocation}`;
  } else {
    // For development or when API_BASE_URL is just the base domain
    const fallbackUrl = "https://staging.namastevillage.theeasyaccess.com";
    // Don't include bucket name as the endpoint handles it automatically
    return `${fallbackUrl}/backend-api/v1/public/user/get-file?location=${itemLocation}`;
  }
};

/**
 * Check if user has default access role
 * @param userId - User ID to check
 * @returns boolean - true if user has default access, false otherwise
 */
const isDefaultAccess = async (userId: any): Promise<boolean> => {
  try {
    // Input validation
    if (!userId) {
      console.log("isDefaultAccess: userId is required");
      return false;
    }

    // Get user from database
    const user = await getUser(userId, false);

    // If user not found, return false
    if (!user || !user.keycloak_auth_id) {
      console.log(`isDefaultAccess: User not found for ID: ${userId}`);
      return false;
    }

    // Get user roles from Keycloak using parameterized query to prevent SQL injection
    const userRoles = await sequelize.query(
      `SELECT ROLE_ID FROM USER_ROLE_MAPPING WHERE USER_ID = :userId`,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { userId: user.keycloak_auth_id },
      }
    );

    // If no roles found, return false
    if (!userRoles || userRoles.length === 0) {
      console.log(
        `isDefaultAccess: No roles found for user: ${user.keycloak_auth_id}`
      );
      return false;
    }

    // Extract role IDs
    const roleIds = userRoles.map((role: any) => role.ROLE_ID);

    // Get role details using parameterized query
    const roles = await sequelize.query(
      `SELECT NAME, DESCRIPTION FROM KEYCLOAK_ROLE WHERE ID IN (:roleIds)`,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { roleIds },
      }
    );

    // Check if user has default creation role
    const hasDefaultRole = roles.some(
      (role: any) =>
        role.NAME === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
        role.DESCRIPTION === global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION
    );

    return hasDefaultRole;
  } catch (error) {
    // Log error in development mode only
    if (process.env.NODE_ENV === "development") {
      console.error("isDefaultAccess Exception:", error);
    }
    return false; // Return false instead of null for consistency
  }
};

/** Get organization logo */
const getOrganizationLogo = async (organization_id: any) => {
  // Get item_location directly in a single query using subquery
  const result = await sequelize.query(
    `
    SELECT i.item_location
    FROM nv_settings s
    LEFT JOIN nv_items i ON i.id = s.value
    WHERE s.setting_status = 'active'
    AND s.organization_id = '${organization_id}'
    AND s.key = 'brand_logo'
    LIMIT 1
  `,
    { type: QueryTypes.SELECT, raw: true }
  );

  // Check if we got a result with item_location
  if (result && result.length > 0 && result[0].item_location) {
    return `${global.config.API_BASE_URL}${result[0].item_location}`;
  }

  return "";
};

const getOrgName = async (orgId: any) => {
  try {
    const selectQuery = `SELECT NAME FROM ORG WHERE ID = '${orgId}'`;
    const orgName = await sequelize.query(selectQuery, {
      type: sequelize.QueryTypes.SELECT,
    });
    return orgName[0].NAME;
  } catch (error) {
    console.error("Error in getOrgName:", error);
    return false;
  }
};

/** Get organization logo */
const getOrganizationContactInfo = async (organization_id: any) => {
  // Get item_location directly in a single query using subquery
  const result = await sequelize.query(
    `
    SELECT s.value
    FROM nv_settings s
    WHERE s.setting_status = 'active'
    AND s.organization_id = '${organization_id}'
    AND s.key = 'phoneno'
    LIMIT 1
  `,
    { type: QueryTypes.SELECT, raw: true }
  );
  // Check if we got a result with item_location
  if (result && result.length > 0 && result[0].value) {
    return result[0].value;
  }
  return "";
};


/**
 * Format date to user-friendly relative time (e.g., "2 days ago", "3 hours ago")
 */
const formatTimeAgo = (date: Date | string): string => {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInMs = now.getTime() - targetDate.getTime();

  // If the date is in the future or invalid, return the formatted date
  if (diffInMs < 0 || isNaN(diffInMs)) {
    return targetDate.toLocaleDateString();
  }

  const diffInSeconds = Math.floor(diffInMs / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (diffInSeconds < 60) {
    return "Just now";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes !== 1 ? "s" : ""} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours !== 1 ? "s" : ""} ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays !== 1 ? "s" : ""} ago`;
  } else if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks !== 1 ? "s" : ""} ago`;
  } else if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths !== 1 ? "s" : ""} ago`;
  } else {
    return `${diffInYears} year${diffInYears !== 1 ? "s" : ""} ago`;
  }
};


/**
 * Helper function to determine platform from request headers
 * @param req - Request object with headers
 * @returns Platform number (1=web, 2=app)
 */
const getPlatformFromRequest = (req: any): number => {
  const platformType = req?.headers?.["platform-type"];
  return platformType == "ios" || platformType == "android" ? 2 : 1; // Default to web (1) if not specified
};


/**
 * Get MORole details by role IDs using raw SQL
 * @param roleIds - Array of role IDs
 * @returns Promise<any[]> - Array of role objects
 */
const getRolesMo = async (roleIds: number[]): Promise<any[]> => {
  try {
    if (!roleIds || roleIds.length === 0) {
      return [];
    }

    const roleQuery = `
      SELECT id, role_name, role_status, organization_id
      FROM mo_roles
      WHERE id IN (${roleIds.map(() => '?').join(',')})
        AND role_status = 'active'
    `;

    const roles = await sequelize.query(roleQuery, {
      replacements: roleIds,
      type: QueryTypes.SELECT,
    });

    return roles || [];
  } catch (error) {
    console.log('Error getting MORole details:', error);
    return [];
  }
};

/**
 * Validate user permissions using raw SQL queries for MORole, MOPermission, and MOModule tables
 * @param user - User object or user ID
 * @param organization_id - Organization ID for filtering
 * @param module_slug - Module slug (e.g., 'dashboard', 'branch', 'staff') to check permission for
 * @param permission_type - Permission type (VIEW=1, CREATE=2, EDIT=4, DELETE=8) from ROLE_PERMISSIONS constant
 * @param platform - Platform type (1=web, 2=app, 3=both) - optional, defaults to web
 * @returns Promise<boolean> - true if user has permission, false otherwise
 */
const validateModulePermission = async (
  user: any,
  organization_id: string,
  module_slug: string,
  permission_type: number,
  platform: number = 1 // Default to web platform
): Promise<boolean|undefined> => {
  try {
    // Extract user ID if user object is passed
    const user_id = typeof user === 'object' ? user.id : user;

    if (!user_id || !module_slug || permission_type === undefined) {
      console.log("validateModulePermission: Missing required parameters");
      return false;
    }

    if (!user.organization_id) {
      return true;
    }

    // Get user details with organization validation using raw SQL
    const userQuery = `
      SELECT id, user_role_id, user_active_role_id, web_user_active_role_id, organization_id
      FROM nv_users
      WHERE id = :user_id
        AND organization_id = :organization_id
        AND user_status NOT IN (:pending, :deleted, :cancelled)
      LIMIT 1
    `;

    const userResult = await sequelize.query(userQuery, {
      replacements: {
        user_id,
        organization_id,
        pending: "pending",
        deleted: "deleted",
        cancelled: "cancelled"
      },
      type: QueryTypes.SELECT,
    });

    if (!userResult || userResult.length === 0) {
      console.log("validateModulePermission: User not found or not in organization");
      return false;
    }

    const userDetails: any = userResult[0];

    // Prioritize user_role_id (MORole), then fallback to old role system
    const active_role_id = userDetails.user_role_id ||
                          (platform === 1 ? userDetails.web_user_active_role_id : userDetails.user_active_role_id);

    if (!active_role_id) {
      console.log("validateModulePermission: User has no active role");
      return false;
    }

    
    if (!userDetails.organization_id) {
      return true
    }

    // If using user_role_id (MORole system), proceed with MORole validation
    if (userDetails.user_role_id && platform == 1) {
      // Check if the role exists and is active in the organization using raw SQL
      const roleQuery = `
        SELECT id, role_name, role_status, platform, additional_permissions
        FROM mo_roles
        WHERE id = :role_id
          AND organization_id = :organization_id
          AND role_status = 'active'
        LIMIT 1
      `;

      const roleResult = await sequelize.query(roleQuery, {
        replacements: {
          role_id: active_role_id,
          organization_id
        },
        type: QueryTypes.SELECT,
      });

      if (!roleResult || roleResult.length === 0) {
        console.log("validateModulePermission: User MORole not found or inactive");
        return false;
      }

      const userRole: any = roleResult[0];

      // Check if the role has access to the requested platform
      const rolePlatform = userRole.platform;
      if (rolePlatform !== 3 && rolePlatform !== platform) {
        console.log(`validateModulePermission: Role platform ${rolePlatform} doesn't match requested platform ${platform}`);
        return false;
      }

      // Check if the module exists globally using raw SQL
      const moduleQuery = `
        SELECT id, module, module_name
        FROM mo_modules
        WHERE module = :module_slug
        LIMIT 1
      `;

      const moduleResult = await sequelize.query(moduleQuery, {
        replacements: {
          module_slug
        },
        type: QueryTypes.SELECT,
      });

      if (!moduleResult || moduleResult.length === 0) {
        console.log(`validateModulePermission: Module '${module_slug}' not found globally`);
        return false;
      }

      const moduleData: any = moduleResult[0];

      // Check if the module is assigned to the organization using raw SQL
      const orgModuleQuery = `
        SELECT id, module_id, organization_id, status
        FROM mo_org_modules
        WHERE module_id = :module_id
          AND organization_id = :organization_id
          AND status = 'active'
        LIMIT 1
      `;

      const orgModuleResult = await sequelize.query(orgModuleQuery, {
        replacements: {
          module_id: moduleData.id,
          organization_id
        },
        type: QueryTypes.SELECT,
      });

      if (!orgModuleResult || orgModuleResult.length === 0) {
        console.log(`validateModulePermission: Module '${module_slug}' not assigned to organization ${organization_id}`);
        return false;
      }

      // Get user's permission for the specific module using raw SQL
      const permissionQuery = `
        SELECT permission, status
        FROM mo_permissions
        WHERE role_id = :role_id
          AND module_id = :module_id
          AND organization_id = :organization_id
          AND status = 'active'
        LIMIT 1
      `;

      const permissionResult = await sequelize.query(permissionQuery, {
        replacements: {
          role_id: active_role_id,
          module_id: moduleData.id,
          organization_id
        },
        type: QueryTypes.SELECT,
      });

      if (!permissionResult || permissionResult.length === 0) {
        console.log("validateModulePermission: No permission record found for user role, module, and platform");
        return false;
      }

      const userPermission: any = permissionResult[0];

      // Check if user has the required permission using bitwise AND
      const hasPermission = (userPermission.permission & permission_type) > 0;

      if (!hasPermission) {
        console.log(`validateModulePermission: User lacks required permission. Required: ${permission_type}, User has: ${userPermission.permission}`);
        return false;
      }

      console.log(`validateModulePermission: Permission granted for user ${user_id}, module ${module_slug}, permission ${permission_type}`);
      return true;
    } else {
      const status = await permittedForAdmin(user_id, [ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.DIRECTOR, ROLE_CONSTANT.HR, ROLE_CONSTANT.AREA_MANAGER, ROLE_CONSTANT.BRANCH_MANAGER, ROLE_CONSTANT.HOTEL_MANAGER, ROLE_CONSTANT.ACCOUNTANT]);
      return status;
    }

  } catch (error) {
    console.log("validateModulePermission error:", error);
    return false;
  }
};

const permittedForAdmin = async (user_id: number, roleArray?: any) => {
  try {
    const query = `
  SELECT ur.*, r.role_name FROM nv_user_roles ur
  INNER JOIN nv_roles r ON ur.role_id = r.id
  WHERE ur.user_id = :user_id
  AND r.role_name IN (:roleArray)
`;

    const roleData = await sequelize.query(query, {
      replacements: { user_id, roleArray },
      type: QueryTypes.SELECT,
    });

    if (roleData.length > 0) {
      const roleNames = roleData.map((role: any) => role.role_name);

      if (
        roleNames.includes(ROLE_CONSTANT.DIRECTOR) ||
        roleNames.includes(ROLE_CONSTANT.HR)
      ) {
        const userQuery = `
      SELECT id FROM nv_users 
      WHERE id = :user_id 
      AND user_status NOT IN (:statuses)
    `;

        const userStatus = await sequelize.query(userQuery, {
          replacements: {
            user_id,
            statuses: ["pending", "deleted", "cancelled"],
          },
          type: QueryTypes.SELECT,
        });

        if (userStatus.length > 0) {
          return true;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

/**
 * Check if user has admin permissions using MORole system (mo_roles table)
 * @param user_id - User ID to check permissions for
 * @param organization_id - Organization ID for filtering
 * @param roleArray - Array of role names to check against (optional)
 * @returns Promise<boolean> - true if user has admin permissions, false otherwise
 */
const permittedForAdminMO = async (
  user_id: number,
  organization_id: string,
  roleArray?: string[]
): Promise<boolean> => {
  try {
    if (!user_id || !organization_id) {
      console.log("permittedForAdminMO: Missing required parameters");
      return false;
    }

    // Default admin roles if none provided
    const defaultAdminRoles = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
      ROLE_CONSTANT.ACCOUNTANT
    ];

    const rolesToCheck = roleArray || defaultAdminRoles;

    // Get user details and role information using raw SQL
    const userRoleQuery = `
      SELECT
        u.id,
        u.user_role_id,
        u.user_status,
        u.organization_id,
        r.id as role_id,
        r.role_name,
        r.role_status,
        r.role_type
      FROM nv_users u
      INNER JOIN mo_roles r ON u.user_role_id = r.id
      WHERE u.id = :user_id
        AND u.organization_id = :organization_id
        AND u.user_status NOT IN (:pending, :deleted, :cancelled)
        AND r.role_status = 'active'
        AND r.organization_id = :organization_id
      LIMIT 1
    `;

    const userRoleResult = await sequelize.query(userRoleQuery, {
      replacements: {
        user_id,
        organization_id,
        pending: "pending",
        deleted: "deleted",
        cancelled: "cancelled"
      },
      type: QueryTypes.SELECT,
    });

    if (!userRoleResult || userRoleResult.length === 0) {
      console.log("permittedForAdminMO: User not found, inactive, or has no valid role");
      return false;
    }

    const userRole: any = userRoleResult[0];

    // Check if user's role is in the permitted admin roles
    const hasAdminRole = rolesToCheck.includes(userRole.role_name);

    if (!hasAdminRole) {
      console.log(`permittedForAdminMO: User role '${userRole.role_name}' is not in permitted admin roles`);
      return false;
    }

    // Additional validation for specific roles (Director, HR)
    if (userRole.role_name === ROLE_CONSTANT.DIRECTOR || userRole.role_name === ROLE_CONSTANT.HR) {
      // For Director and HR roles, ensure user status is valid
      const userStatusQuery = `
        SELECT id
        FROM nv_users
        WHERE id = :user_id
          AND organization_id = :organization_id
          AND user_status NOT IN (:pending, :deleted, :cancelled)
        LIMIT 1
      `;

      const userStatusResult = await sequelize.query(userStatusQuery, {
        replacements: {
          user_id,
          organization_id,
          pending: "pending",
          deleted: "deleted",
          cancelled: "cancelled"
        },
        type: QueryTypes.SELECT,
      });

      if (!userStatusResult || userStatusResult.length === 0) {
        console.log("permittedForAdminMO: Director/HR user status validation failed");
        return false;
      }
    }

    console.log(`permittedForAdminMO: User ${user_id} has admin permission with role '${userRole.role_name}'`);
    return true;

  } catch (error) {
    console.log("permittedForAdminMO error:", error);
    return false;
  }
};

export {
  getUserIdFromUserTable,
  getPaginatedItems,
  getPagination,
  readHTMLFile,
  checkUserRole,
  getRoles,
  getUser,
  getUserFullName,
  getUserAllRoles,
  getUserSession,
  createNotification,
  getBranchDetails,
  getUsers,
  getDepartmentDetails,
  getUserWeekDays,
  getHash,
  ReadingFile,
  getMimeTypeFromExtension,
  getImageUrl,
  isDefaultAccess,
  getUserLiterals,
  getEnhancedUserLiterals,
  getOrganizationLogo,
  getOrgName,
  getPlatformFromRequest,
  getRolesMo,
  validateModulePermission,
  permittedForAdmin,
  permittedForAdminMO,
  formatTimeAgo,
  getOrganizationContactInfo
};
