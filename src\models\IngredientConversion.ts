import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum IngredientConversionStatus {
  active = "active",
  inactive = "inactive",
}

export class IngredientConversion extends Model {
  ingredient_id!: number;
  from_measure!: number;
  from_measure_value!: number;
  to_measure!: number;
  to_measure_value!: number;
  ingredient_conversion_status!: IngredientConversionStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
}

IngredientConversion.init(
  {
    ingredient_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_ingredients",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    from_measure: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_recipe_measure",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    from_measure_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    to_measure: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_recipe_measure",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    to_measure_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    ingredient_conversion_status: {
      type: DataTypes.ENUM,
      values: Object.values(IngredientConversionStatus),
      defaultValue: IngredientConversionStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_ingredients_conversion",
    modelName: "IngredientConversion",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    // Removed unique constraint to allow multiple conversions with same from/to measures
    // indexes: [
    //     {
    //         unique: true,
    //         fields: ['ingredient_id', 'from_measure', 'to_measure']
    //     }
    // ]
  },
);

// Remove the automatic 'id' attribute for junction table
IngredientConversion.removeAttribute("id");

// Define associations
IngredientConversion.associate = (models: any) => {
  // IngredientConversion belongs to Ingredient
  IngredientConversion.belongsTo(models.Ingredient, {
    foreignKey: "ingredient_id",
    as: "ingredient",
  });

  // IngredientConversion belongs to RecipeMeasure (from_measure)
  IngredientConversion.belongsTo(models.RecipeMeasure, {
    foreignKey: "from_measure",
    as: "fromUnit",
  });

  // IngredientConversion belongs to RecipeMeasure (to_measure)
  IngredientConversion.belongsTo(models.RecipeMeasure, {
    foreignKey: "to_measure",
    as: "toUnit",
  });
};
