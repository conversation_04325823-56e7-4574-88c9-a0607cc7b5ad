import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum IngredientStatus {
    active = "active",
    inactive = "inactive",
}

interface IngredientAttributes {
    id?: number;
    ingredient_name: string;
    ingredient_slug: string;
    ingredient_description?: string;
    ingredient_status: IngredientStatus;
    waste_percentage?: number;
    unit_of_measure?: number;
    unit_conversion_capabilities?: number;
    cost_per_unit: number;
    cost_last_updated_at?: Date;
    nutrition_last_updated_at?: Date;
    organization_id?: string;
    created_by: number;
    updated_by: number;
}

export class Ingredient
    extends Model<IngredientAttributes, never>
    implements IngredientAttributes {
    id!: number;
    ingredient_name!: string;
    ingredient_slug!: string;
    ingredient_description?: string;
    ingredient_status!: IngredientStatus;
    waste_percentage?: number;
    unit_of_measure?: number;
    unit_conversion_capabilities?: number;
    cost_per_unit!: number;
    cost_last_updated_at?: Date;
    nutrition_last_updated_at?: Date;
    organization_id?: string;
    created_by!: number;
    updated_by!: number;
}

Ingredient.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ingredient_name: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        ingredient_slug: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        ingredient_description: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        ingredient_status: {
            type: DataTypes.ENUM,
            values: Object.values(IngredientStatus),
            defaultValue: IngredientStatus.active,
        },
        waste_percentage: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        unit_of_measure: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        cost_per_unit: {
            type: DataTypes.FLOAT,
            allowNull: false,
        },
        cost_last_updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
            comment: "Timestamp when cost_per_unit was last updated",
        },
        nutrition_last_updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
            comment: "Timestamp when nutrition attributes were last updated",
        },
        organization_id: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
    },
    {
        sequelize,
        tableName: "mo_ingredients",
        modelName: "Ingredient",
        timestamps: true,
        createdAt: "created_at",
        updatedAt: "updated_at",
    }
);

// Define associations
Ingredient.associate = (models: any) => {
    // User associations
    Ingredient.belongsTo(models.User, {
        foreignKey: "created_by",
        as: "creator",
    });

    Ingredient.belongsTo(models.User, {
        foreignKey: "updated_by",
        as: "updater",
    });

    // Unit of measure association
    Ingredient.belongsTo(models.RecipeMeasure, {
        foreignKey: "unit_of_measure",
        as: "unit",
    });

    // Many-to-many association with Category through IngredientCategory
    Ingredient.belongsToMany(models.Category, {
        through: models.IngredientCategory,
        foreignKey: "ingredient_id",
        otherKey: "category_id",
        as: "categories"
    });

    // Many-to-many association with FoodAttributes through IngredientAttributes
    Ingredient.belongsToMany(models.FoodAttributes, {
        through: models.IngredientAttributes,
        foreignKey: "ingredient_id",
        otherKey: "attributes_id",
        as: "attributes"
    });

    // Many-to-many association with Recipe through RecipeIngredients
    Ingredient.belongsToMany(models.Recipe, {
        through: models.RecipeIngredients,
        foreignKey: "ingredient_id",
        otherKey: "recipe_id",
        as: "recipes"
    });

    // One-to-many association with IngredientConversion
    Ingredient.hasMany(models.IngredientConversion, {
        foreignKey: "ingredient_id",
        as: "conversions"
    });
};

export default Ingredient;