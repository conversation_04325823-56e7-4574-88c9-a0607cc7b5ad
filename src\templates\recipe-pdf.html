<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe: {{RECIPE_TITLE}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #ffffff;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        /* Header Section */
        .header {
            background: linear-gradient(135deg, #135e96 0%, #0f4a7a 100%);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 15px 15px;
            position: relative;
            overflow: hidden;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .logo-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-right: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .company-info h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .company-info p {
            font-size: 16px;
            opacity: 0.9;
        }

        /* Recipe Title Section */
        .recipe-title-section {
            text-align: center;
            margin: 30px 0 20px 0;
            padding: 40px 30px 30px 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 3px solid #135e96;
            position: relative;
            min-height: 120px;
        }

        .recipe-title-section::before {
            content: '🍽️';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 10px;
            border-radius: 50%;
            font-size: 24px;
            border: 3px solid #135e96;
            z-index: 1;
        }

        .recipe-title {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            margin-top: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
            line-height: 1.2;
        }

        .recipe-subtitle {
            font-size: 18px;
            color: #7f8c8d;
            font-style: italic;
            position: relative;
            z-index: 2;
        }

        /* Recipe Image */
        .recipe-image {
            text-align: center;
            margin: 20px 0;
            max-height: 300px;
            overflow: hidden;
        }

        .recipe-main-image {
            max-width: 100%;
            max-height: 300px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
            display: block;
        }

        .image-placeholder {
            width: 300px;
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 3px dashed #135e96;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #7f8c8d;
            margin: 0 auto;
        }

        /* Section Headers */
        .section {
            margin: 20px 0;
            page-break-inside: avoid;
        }

        .section:first-of-type {
            margin-top: 10px;
        }

        .section:last-of-type {
            margin-bottom: 10px;
        }

        .section-header {
            background: linear-gradient(135deg, #135e96 0%, #0f4a7a 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .section-header .icon {
            margin-right: 15px;
            font-size: 24px;
        }

        /* Info Cards Grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin: 15px 0;
        }

        .info-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-2px);
        }

        .info-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .info-card-icon {
            font-size: 20px;
            margin-right: 10px;
        }

        .info-card-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .info-card-value {
            font-size: 18px;
            color: #27ae60;
            font-weight: 700;
        }

        /* Categories */
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 20px 0;
        }

        .category-badge {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        /* Ingredients Table */
        .ingredients-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .ingredients-table thead {
            background: linear-gradient(135deg, #135e96 0%, #0f4a7a 100%);
            color: white;
        }

        .ingredients-table th,
        .ingredients-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .ingredients-table th {
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .ingredients-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .ingredients-table tbody tr:nth-child(odd) {
            background: #ffffff;
        }

        .ingredients-table tbody tr:hover {
            background: #e8f4fd !important;
        }

        /* Ingredients table footer for totals */
        .ingredients-table tfoot {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
            font-weight: 700;
        }

        .ingredients-table tfoot td {
            padding: 15px;
            font-size: 16px;
            border-bottom: none;
        }

        /* Specific column alignments */
        .ingredients-table .ingredient-name {
            text-align: left;
            font-weight: 600;
        }

        .ingredients-table .ingredient-quantity,
        .ingredients-table .ingredient-unit,
        .ingredients-table .ingredient-cost {
            text-align: center;
            font-weight: 500;
        }

        /* Total row styling */
        .total-row {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
        }

        .total-row td {
            border-top: 3px solid #135e96 !important;
            font-size: 16px !important;
            padding: 15px !important;
            color: #ffffff !important;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        /* Enhanced table styling for better readability */
        .ingredients-table tbody tr {
            transition: background-color 0.2s ease;
        }

        .ingredients-table tbody tr:hover {
            background: #e3f2fd !important;
            transform: scale(1.01);
        }

        /* Cost column highlighting */
        .ingredient-cost {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #d35400;
            background: rgba(211, 84, 0, 0.1);
            border-radius: 4px;
        }

        /* Quantity column styling */
        .ingredient-quantity {
            font-weight: 600;
            color: #2980b9;
            background: rgba(41, 128, 185, 0.1);
            border-radius: 4px;
        }

        /* Unit column styling */
        .ingredient-unit {
            font-style: italic;
            color: #8e44ad;
            font-weight: 500;
        }

        /* Ingredient name styling */
        .ingredient-name {
            color: #2c3e50;
            font-weight: 600;
        }

        /* Add subtle borders for better separation */
        .ingredients-table td {
            border-right: 1px solid #e9ecef;
        }

        .ingredients-table td:last-child {
            border-right: none;
        }

        /* Steps */
        .steps-container {
            margin: 15px 0;
        }

        .step {
            display: flex;
            margin: 15px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            border-left: 5px solid #135e96;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            page-break-inside: avoid;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #135e96 0%, #0f4a7a 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .step-description {
            font-size: 14px;
            line-height: 1.6;
            color: #34495e;
        }

        .step-image {
            margin-top: 10px;
            max-height: 200px;
            overflow: hidden;
            text-align: center;
        }

        .step-image img {
            max-width: 100%;
            max-height: 200px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Tips and Serving */
        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 12px;
            margin: 15px 0;
        }

        .tip-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-top: 4px solid #e67e22;
        }

        .tip-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .tip-icon {
            font-size: 24px;
            margin-right: 15px;
        }

        .tip-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .tip-content {
            font-size: 14px;
            line-height: 1.6;
            color: #34495e;
        }

        /* Resources Grid */
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .resource-card {
            background: white;
            border: 1px solid #e0e6ed;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-top: 4px solid #135e96;
            min-height: 120px;
            display: flex;
            flex-direction: column;
        }

        .resource-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .resource-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .resource-icon {
            font-size: 20px;
            margin-right: 10px;
            color: #135e96;
        }

        .resource-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
            flex: 1;
        }

        .resource-description {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 12px;
            line-height: 1.4;
            flex: 1;
        }

        .resource-link {
            margin-top: auto;
        }

        .resource-link a {
            display: inline-block;
            background: #135e96;
            color: white !important;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .resource-link a:hover {
            background: #0f4a7a;
        }

        /* Ensure all links are properly styled for PDF */
        a[href] {
            color: #135e96 !important;
            text-decoration: underline !important;
            font-weight: 600 !important;
        }

        /* Special styling for resource links */
        .resource-link a[href] {
            display: inline-block !important;
            padding: 8px 16px !important;
            background: #f8f9fa !important;
            border: 2px solid #135e96 !important;
            border-radius: 6px !important;
            color: #135e96 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
        }

        .resource-image {
            max-width: 100%;
            max-height: 150px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .image-fallback {
            width: 100%;
            height: 150px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
            margin: 10px 0;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            margin: 40px -20px -20px -20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-bottom: 20px;
        }

        .footer-section h4 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #3498db;
            font-weight: 700;
        }

        .footer-section p {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .footer-section p strong {
            color: #ffffff;
            font-weight: 700;
        }

        /* Center organization name in footer */
        .footer-section:first-child {
            text-align: center;
        }

        .footer-section:first-child h4 {
            text-align: center;
        }

        .footer-section:first-child p {
            text-align: center;
        }

        /* Print Styles */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
                line-height: 1.4;
            }

            .container {
                max-width: none;
                margin: 0;
                padding: 10px;
            }

            .section {
                page-break-inside: avoid;
                margin: 10px 0;
            }

            .step {
                page-break-inside: avoid;
                margin: 10px 0;
                padding: 15px;
            }

            .resource-card {
                page-break-inside: avoid;
                margin: 8px 0;
                padding: 15px;
            }

            .tip-card {
                page-break-inside: avoid;
                margin: 8px 0;
                padding: 15px;
            }

            .info-card {
                margin: 8px 0;
                padding: 12px;
            }

            /* Ensure links are visible and clickable in print */
            a {
                color: #135e96 !important;
                text-decoration: underline !important;
                font-weight: 600 !important;
                border: none !important;
                background: none !important;
            }

            /* Reduce spacing for print */
            .recipe-title-section {
                margin: 15px 0;
                padding: 30px 20px 20px 20px;
            }

            .header {
                margin: -10px -10px 15px -10px;
                padding: 15px;
            }

            .footer {
                margin: 15px -10px -10px -10px;
                padding: 15px;
            }

            .section-header {
                margin-bottom: 10px;
                padding: 10px 15px;
                font-size: 16px;
            }

            .info-grid,
            .categories-grid,
            .tips-grid,
            .resources-grid {
                margin: 10px 0;
                gap: 8px;
            }

            .ingredients-table {
                margin: 10px 0;
                page-break-inside: avoid;
            }

            .ingredients-table th,
            .ingredients-table td {
                padding: 10px 12px;
                font-size: 14px;
            }

            .total-row td {
                padding: 12px !important;
                font-size: 15px !important;
                background: #2c3e50 !important;
                color: #ffffff !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
            }

            /* Ensure colors are preserved in print */
            .ingredient-cost {
                color: #d35400 !important;
                background: rgba(211, 84, 0, 0.1) !important;
            }

            .ingredient-quantity {
                color: #2980b9 !important;
                background: rgba(41, 128, 185, 0.1) !important;
            }

            .ingredient-unit {
                color: #8e44ad !important;
            }

            .ingredient-name {
                color: #2c3e50 !important;
            }

            /* Ensure table doesn't break across pages */
            .ingredients-table tbody tr {
                page-break-inside: avoid;
            }

            .steps-container {
                margin: 10px 0;
            }

            /* Ensure images don't break layout */
            img {
                max-width: 100% !important;
                height: auto !important;
                object-fit: contain !important;
                page-break-inside: avoid;
            }

            /* Remove extra spacing */
            .hidden {
                display: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }
        }

        /* Utility Classes */
        .text-center {
            text-align: center;
        }

        .text-bold {
            font-weight: 700;
        }

        .text-muted {
            color: #7f8c8d;
        }

        .mb-20 {
            margin-bottom: 20px;
        }

        .mt-20 {
            margin-top: 20px;
        }

        .hidden {
            display: none;
        }

        /* Additional fixes for PDF generation */
        img {
            max-width: 100% !important;
            height: auto !important;
            object-fit: contain !important;
            display: block;
        }

        /* Ensure proper link styling */
        a {
            color: #135e96 !important;
            text-decoration: underline !important;
            font-weight: 500;
        }

        /* Remove extra margins that cause spacing issues */
        .section:empty,
        .section .hidden {
            display: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Compact spacing for PDF */
        .recipe-title-section {
            margin: 15px 0;
        }

        .info-grid {
            margin: 12px 0;
        }

        .categories-grid,
        .tips-grid,
        .resources-grid {
            margin: 12px 0;
        }

        .ingredients-table {
            margin: 12px 0;
        }

        .steps-container {
            margin: 12px 0;
        }

        .step {
            margin: 12px 0;
        }

        /* Description Content */
        .description-content {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-size: 16px;
            line-height: 1.6;
            color: #2c3e50;
        }

        .description-content p {
            margin: 0;
            text-align: justify;
        }

        /* HACCP Content */
        .haccp-content {
            margin: 15px 0;
        }

        .haccp-item {
            background: white;
            border: 2px solid #e74c3c;
            border-radius: 12px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.1);
        }

        .haccp-item-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .haccp-item-icon {
            font-size: 20px;
            margin-right: 10px;
            color: #e74c3c;
        }

        .haccp-item-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .haccp-item-description {
            color: #34495e;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Enhanced ingredient preparation column */
        .ingredient-preparation {
            text-align: left;
            font-style: italic;
            color: #7f8c8d;
            font-size: 13px;
            line-height: 1.4;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo {{ORG_LOGO_HIDDEN}}">
                        <img src="{{ORGANIZATION_LOGO}}" alt="Organization Logo"
                            style="width: 100%; height: 100%; object-fit: contain; border-radius: 8px;">
                    </div>
                    <div class="logo {{ORG_LOGO_VISIBLE}}">🍳</div>
                    <div class="company-info">
                        <h1 style="display: block; text-align: center;">{{ORGANIZATION_NAME}}</h1>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recipe Title Section -->
        <div class="recipe-title-section">
            <h1 class="recipe-title">{{RECIPE_TITLE}}</h1>
            <p class="recipe-subtitle {{RECIPE_SUBTITLE_HIDDEN}}">"{{RECIPE_SUBTITLE}}"</p>
        </div>

        <!-- Recipe Image -->
        <div class="recipe-image {{RECIPE_IMAGE_HIDDEN}}">
            <img src="{{RECIPE_IMAGE_URL}}" alt="{{RECIPE_TITLE}}" class="recipe-main-image">
        </div>

        <!-- Recipe Description -->
        <div class="section {{RECIPE_DESCRIPTION_HIDDEN}}">
            <div class="section-header">
                <span class="icon">📝</span>
                Recipe Description
            </div>
            <div class="description-content">
                <p>{{RECIPE_DESCRIPTION}}</p>
            </div>
        </div>

        <!-- Recipe Information -->
        <div class="section">
            <div class="section-header">
                <span class="icon">📋</span>
                Recipe Information
            </div>
            <div class="info-grid">
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">🏷️</span>
                        <span class="info-card-label">Status</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_STATUS}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">⏱️</span>
                        <span class="info-card-label">Prep Time</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_PREP_TIME}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">🔥</span>
                        <span class="info-card-label">Cook Time</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_COOK_TIME}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">⏰</span>
                        <span class="info-card-label">Total Time</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_TOTAL_TIME}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">📊</span>
                        <span class="info-card-label">Complexity</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_COMPLEXITY}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">🍽️</span>
                        <span class="info-card-label">Servings</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_YIELD}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">👥</span>
                        <span class="info-card-label">Total Portions</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_PORTIONS}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-header">
                        <span class="info-card-icon">🥄</span>
                        <span class="info-card-label">Per Serving Unit</span>
                    </div>
                    <div class="info-card-value">{{RECIPE_SERVING_SIZE}}</div>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="section {{CATEGORIES_HIDDEN}}">
            <div class="section-header">
                <span class="icon">🏷️</span>
                Categories
            </div>
            <div class="categories-grid">
                {{CATEGORIES_LIST}}
            </div>
        </div>

        <!-- Ingredients -->
        <div class="section {{INGREDIENTS_HIDDEN}}">
            <div class="section-header">
                <span class="icon">🥘</span>
                Ingredients
            </div>
            <table class="ingredients-table">
                <thead>
                    <tr>
                        <th style="width: 30%;">🥘 Ingredient</th>
                        <th style="width: 15%;">📏 Quantity</th>
                        <th style="width: 15%;">⚖️ Unit</th>
                        <th style="width: 25%;">🔪 Preparation</th>
                        <th style="width: 15%;">💰 Cost</th>
                    </tr>
                </thead>
                <tbody>
                    {{INGREDIENTS_LIST}}
                </tbody>
                <tfoot>
                    {{INGREDIENTS_TOTALS}}
                </tfoot>
            </table>
        </div>

        <!-- Preparation Steps -->
        <div class="section {{STEPS_HIDDEN}}">
            <div class="section-header">
                <span class="icon">👨‍🍳</span>
                Preparation Steps
            </div>
            <div class="steps-container">
                {{STEPS_LIST}}
            </div>
        </div>

        <!-- Tips & Serving -->
        <div class="section {{TIPS_HIDDEN}}">
            <div class="section-header">
                <span class="icon">💡</span>
                Tips & Serving
            </div>
            <div class="tips-grid">
                {{TIPS_LIST}}
            </div>
        </div>

        <!-- Nutritional & Dietary Information -->
        <div class="section {{NUTRITION_HIDDEN}}">
            <div class="section-header">
                <span class="icon">🥗</span>
                Nutritional & Dietary Information
            </div>
            {{NUTRITION_CONTENT}}
        </div>

        <!-- HACCP Information -->
        <div class="section {{HACCP_HIDDEN}}">
            <div class="section-header">
                <span class="icon">🛡️</span>
                HACCP Information
            </div>
            <div class="haccp-content">
                {{HACCP_CONTENT}}
            </div>
        </div>

        <!-- Resources & Attachments -->
        <div class="section {{RESOURCES_HIDDEN}}">
            <div class="section-header">
                <span class="icon">📎</span>
                Resources & Attachments
            </div>
            <div class="resources-grid">
                {{RESOURCES_LIST}}
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-grid">
                <div class="footer-section">
                    <h4>📄 Document Information</h4>
                    <p>Generated: {{GENERATION_DATE}}</p>
                    <p>Organization: {{ORGANIZATION_NAME}}</p>
                </div>
            </div>
        </div>
    </div>
</body>

</html>