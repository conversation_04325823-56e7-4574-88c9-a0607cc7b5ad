import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum IngredientAttributesStatus {
  active = "active",
  inactive = "inactive",
}

export class IngredientAttributes extends Model {
  ingredient_id!: number;
  attributes_id!: string;
  ingredient_attributes_status!: IngredientAttributesStatus;
  unit_of_measure?: string;
  unit?: number;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
}

IngredientAttributes.init(
  {
    ingredient_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_ingredients",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    attributes_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_food_attributes",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
    },
    ingredient_attributes_status: {
      type: DataTypes.ENUM,
      values: Object.values(IngredientAttributesStatus),
      defaultValue: IngredientAttributesStatus.active,
    },
    unit_of_measure: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "Unit of measure as text (e.g., 'grams', 'cups', 'tablespoons')",
    },
    unit: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_ingredients_attributes",
    modelName: "IngredientAttributes",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["ingredient_id", "attributes_id"],
        name: "primary_ingredient_attributes",
      },
      {
        fields: ["organization_id"],
        name: "idx_ingredient_attributes_organization",
      },
      {
        fields: ["ingredient_attributes_status"],
        name: "idx_ingredient_attributes_status",
      },
    ],
  }
);

// Define associations
IngredientAttributes.associate = (models: any) => {
  // IngredientAttributes belongs to Ingredient
  IngredientAttributes.belongsTo(models.Ingredient, {
    foreignKey: "ingredient_id",
    as: "ingredient",
  });

  // IngredientAttributes belongs to FoodAttributes
  IngredientAttributes.belongsTo(models.FoodAttributes, {
    foreignKey: "attributes_id",
    as: "attribute",
  });

  // Note: unit_of_measure is now a string field, no foreign key relationship
};
