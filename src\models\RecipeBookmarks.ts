import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeBookmarkStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeBookmarkAttributes {
  id?: number;
  recipe_id: number;
  user_id: number;
  status: RecipeBookmarkStatus;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeBookmarks
  extends Model<RecipeBookmarkAttributes, never>
  implements RecipeBookmarkAttributes {
  id!: number;
  recipe_id!: number;
  user_id!: number;
  status!: RecipeBookmarkStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeBookmarks.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "users",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeBookmarkStatus)),
      allowNull: false,
      defaultValue: RecipeBookmarkStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_bookmarks",
    modelName: "RecipeBookmarks",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "user_id"],
        name: "unique_recipe_user_bookmark",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_bookmarks_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_bookmarks_status",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_bookmarks_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_bookmarks_updated_by",
      },
      {
        fields: ["user_id"],
        name: "idx_recipe_bookmarks_user_id",
      },
      {
        fields: ["recipe_id"],
        name: "idx_recipe_bookmarks_recipe_id",
      },
    ],
  }
);

// Define associations
RecipeBookmarks.associate = (models: any) => {
  // RecipeBookmarks belongs to Recipe
  RecipeBookmarks.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeBookmarks belongs to User (user_id)
  RecipeBookmarks.belongsTo(models.User, {
    foreignKey: "user_id",
    as: "user",
  });

  // RecipeBookmarks belongs to User (created_by)
  RecipeBookmarks.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeBookmarks belongs to User (updated_by)
  RecipeBookmarks.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });
};

export default RecipeBookmarks;
