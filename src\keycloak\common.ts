import axios from "axios";

const getUserData = async (url: any, token: any = null) => {
  try {
    // Fetch the token
    const response: any = await makeRequest(url, "GET", null, {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    });
    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    if (response.data && response.data.attributes) {
      const attributes = response.data.attributes;
      // Transform the attributes by converting array values to single values
      const transformedAttributes: any = {};
      Object.keys(attributes).forEach((key) => {
        // Take the first element of the array or keep the value if it's already a single value
        transformedAttributes[key] = Array.isArray(attributes[key])
          ? attributes[key][0]
          : attributes[key];
      });

      // Update the response with the transformed attributes
      response.data.attributes = transformedAttributes;
    }
    return {
      status: true,
      message: response,
      statusText: response.statusText,
      data: response.data,
    }; // Return true if the user is created successfully in Keycloak
  } catch (e: any) {
    console.log("Get data By Id Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

const makeRequest = async (
  url: any,
  method: any,
  payload: any = null,
  headers: any = null,
) => {
  try {
    const config: any = {
      method,
      url,
      headers: headers,
    };
    if (payload) {
      config.data = payload; // Attach the payload for methods like POST, PUT, etc.
    }
    const response = await axios(config);
    return response; // Return the response data
  } catch (e: any) {
    console.log("axios Exception: ", e.response); // Log any exceptions that occur
    if (e.response && e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.data.error,
        field: e.response.data?.field,
      }; // Return false if an exception is caught
    }
  }
};

const getOrganizationData = async (url: any, token: any = null) => {
  try {
    const response: any = await makeRequest(url, "GET", null, {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    });
    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    if (response.data && response.data.attributes) {
      const attributes = response.data.attributes;
      // Transform the attributes by converting array values to single values
      const transformedAttributes: any = {};
      Object.keys(attributes).forEach((key) => {
        // Take the first element of the array or keep the value if it's already a single value
        transformedAttributes[key] = Array.isArray(attributes[key])
          ? attributes[key][0]
          : attributes[key];
      });

      // Update the response with the transformed attributes
      response.data.attributes = transformedAttributes;
    }
    return {
      status: true,
      message: response,
      statusText: response.statusText,
      data: response.data,
    }; // Return true if the user is created successfully in Keycloak
  } catch (e: any) {
    console.log("axios Exception: ", e.response); // Log any exceptions that occur
    if (e.response && e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.data.error,
        field: e.response.data?.field,
      }; // Return false if an exception is caught
    }
  }
};

/** Get user assigned roles */
const getUserRoles = async (userId: any, token: any) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}/role-mappings`;
    const response: any = await makeRequest(keycloakRealmUrl, "GET", null, {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    });

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return {
      status: true,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (e: any) {
    console.log("Get User Roles Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};
export { getUserData, makeRequest, getOrganizationData, getUserRoles };
