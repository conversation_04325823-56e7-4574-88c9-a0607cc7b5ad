import * as cron from 'node-cron';
import { ingredientCostUpdateCronJob } from './ingredientCostUpdateCron';

/**
 * Initialize and start all cron jobs
 */
export const initializeCronJobs = (): void => {
  // Recipe Cost Freshness Cron Job
  // Runs based on COST_RECALCULATION_CRON configuration
  // const costRecalculationSchedule = global.config.COST_RECALCULATION_CRON || '0 * * * *'; // Default: every hour

  // cron.schedule(costRecalculationSchedule, async () => {
  //   try {
  //     await recipeCostFreshnessCronJob();
  //   } catch {
  //     // Silent error handling
  //   }
  // }, {
  //   scheduled: true,
  //   timezone: global.config.TIMEZONE || 'UTC'
  // });

  // Ingredient Cost Update Cron Job
  // Runs every 2 minutes to detect ingredient cost changes and cascade to recipes
  const ingredientUpdateSchedule = global.config.COST_RECALCULATION_CRON || '0 0 * * *'; // Default: every hour
  cron.schedule(ingredientUpdateSchedule, async () => {
    try {
      await ingredientCostUpdateCronJob();
      console.log('Ingredient cost update cron job completed successfully');
    } catch {
      // Silent error handling
      console.error('Ingredient cost update cron job failed');
    }
  }, {
    scheduled: true,
    timezone: global.config.TIMEZONE || 'UTC'
  });
};

/**
 * Stop all cron jobs (useful for graceful shutdown)
 */
export const stopAllCronJobs = (): void => {
  cron.getTasks().forEach((task: any) => {
    task.stop();
  });
};

export default {
  initializeCronJobs,
  stopAllCronJobs
};
