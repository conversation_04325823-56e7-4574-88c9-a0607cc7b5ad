const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if measurement units already exist
    const existingUnits = await queryInterface.sequelize.query(
      `SELECT * FROM mo_recipe_measure WHERE organization_id IS NULL AND is_system_unit = true`,
      { type: QueryTypes.SELECT }
    );

    if (existingUnits.length === 0) {
      // Measurement Units
      const measurementUnits = [
        { name: "g", slug: "g" },
        { name: "kg", slug: "kg" },
        { name: "ml", slug: "ml" },
        { name: "liter", slug: "liter" },
        { name: "unit", slug: "unit" },
        { name: "oz (ounce)", slug: "oz" },
        { name: "lb (pound)", slug: "lb" },
        { name: "tbsp (tablespoon)", slug: "tbsp" },
        { name: "tsp (teaspoon)", slug: "tsp" },
        { name: "cup", slug: "cup" },
        { name: "pint", slug: "pint" },
        { name: "quart", slug: "quart" },
        { name: "gallon", slug: "gallon" },
        { name: "each", slug: "each" },
        { name: "clove", slug: "clove" },
        { name: "ball", slug: "ball" },
        { name: "slice", slug: "slice" },
        { name: "serving", slug: "serving" },
        { name: "leaf", slug: "leaf" },
        { name: "pinch", slug: "pinch" },
        { name: "wedge", slug: "wedge" },
        { name: "sprig", slug: "sprig" },
        { name: "pack", slug: "pack" },
        { name: "case", slug: "case" },
        { name: "dozen", slug: "dozen" },
      ];

      // Prepare bulk insert data
      const unitData = measurementUnits.map((unit) => ({
        unit_title: unit.name,
        unit_slug: unit.slug,
        unit_icon: null,
        status: "active",
        organization_id: null,
        is_system_unit: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("mo_recipe_measure", unitData);
      console.log("✅ Recipe Measurement Units seeded successfully");
      console.log(
        "ℹ️  Note: Recipe measurement unit icons are not available yet - consider adding icons to src/icons/units/"
      );
    } else {
      console.log("⏭️  Recipe Measurement Units already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_recipe_measure", {
      organization_id: null,
      is_system_unit: true,
    });
  },
};
